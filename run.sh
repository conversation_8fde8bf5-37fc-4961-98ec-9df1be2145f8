#!/bin/bash

# 运行脚本
# 使用方法: ./run.sh

echo "=== 开始执行序列 ==="

# 1. 停止相关进程
echo "1. 停止相关进程..."
pkill -f "scripts/parallel_initialize_db.py"
pkill -f "scripts/initialize_db.py"
pkill -f "src/main.py"
pkill -f "experiment/main.py"
pkill -f "experiment/analyze_beliefs.py"
echo "   已停止相关进程"

# 2. 清理日志文件
echo "2. 清理日志文件..."
if [ -f "init_db.log" ]; then
    rm -f init_db.log
    echo "   已删除init_db.log"
fi
if [ -f "main.log" ]; then
    rm -f main.log
    echo "   已删除main.log"
fi

# 3. 运行initialize_db并等待完成
echo "3. 运行数据库初始化..."
nohup python scripts/trait_based_initialize_db.py > init_db.log 2>&1 &
init_pid=$!
echo "   初始化进程启动 (PID: $init_pid)"
echo "   日志文件: init_db.log"

# 等待初始化完成
wait $init_pid
init_exit_code=$?

# 检查初始化是否真正完成（通过检查日志文件）
if grep -q "并行数据库初始化完成!" init_db.log; then
    echo "   数据库初始化完成"
elif [ $init_exit_code -eq 0 ]; then
    echo "   数据库初始化完成"
else
    echo "   数据库初始化失败，请检查init_db.log"
    echo "   最后几行日志："
    tail -10 init_db.log
    exit 1
fi

# 4. 运行main程序并等待完成
echo "4. 启动主程序..."
nohup python src/main.py > main.log 2>&1 &
main_pid=$!
echo "   主程序已启动 (PID: $main_pid)"
echo "   日志文件: main.log"

# 等待主程序完成
echo "   等待主程序完成..."
wait $main_pid
if [ $? -eq 0 ]; then
    echo "   主程序执行完成"
else
    echo "   主程序执行失败，请检查main.log"
    exit 1
fi

# 5. 运行信念分析程序
echo "5. 启动信念分析程序..."
if [ -f "analyze_beliefs.log" ]; then
    rm -f analyze_beliefs.log
    echo "   已删除analyze_beliefs.log"
fi
nohup python experiment/analyze_beliefs.py > analyze_beliefs.log 2>&1 &
analyze_pid=$!
echo "   信念分析程序已启动 (PID: $analyze_pid)"

echo "=== 执行完成 ==="
echo "日志文件:"
echo "  - 数据库初始化: init_db.log"
echo "  - 主程序运行: main.log"
echo "  - 信念分析: analyze_beliefs.log"
echo "查看日志:"
echo "  - tail -f init_db.log"
echo "  - tail -f main.log"
echo "  - tail -f analyze_beliefs.log"
echo "停止程序:"
echo "  - pkill -f \"src/main.py\""
echo "  - pkill -f \"experiment/analyze_beliefs.py\""
