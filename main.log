2025-08-26 19:51:57,095 - Main - INFO - 启动认知智能体模拟系统...
2025-08-26 19:51:57,095 - Engine - INFO - Initializing Engine and its modules...
2025-08-26 19:51:57,095 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 19:51:57,095 - src.modules.content_module - INFO - ContentModule initialized.
2025-08-26 19:51:57,095 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 19:51:57,095 - NotificationModule - INFO - NotificationModule initialized.
2025-08-26 19:51:57,095 - src.modules.belief_module - INFO - BeliefModule initialized with dependencies.
2025-08-26 19:51:57,095 - src.modules.belief_module - INFO - 多线程配置: {'enabled': True, 'max_workers': 4, 'timeout': 30}
2025-08-26 19:51:57,095 - UserManagement - INFO - UserManagementModule initialized.
2025-08-26 19:51:57,095 - Engine - INFO - Engine and modules initialized successfully.
2025-08-26 19:51:57,096 - Simulator - INFO - Simulator initialized with initial_bias_strength=1.
2025-08-26 19:51:57,096 - NotificationModule - INFO - Simulator set for NotificationModule.
2025-08-26 19:51:57,096 - Engine - INFO - Simulator setup completed and linked with notification module.
2025-08-26 19:51:57,096 - BeliefLogger - INFO - 实验结果将保存到: /home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250826_195157
2025-08-26 19:51:57,097 - BeliefLogger - INFO - 实验配置已保存到: experiment_config.json
2025-08-26 19:51:57,097 - Main - INFO - 开始运行 40 轮模拟，每轮 5 步...
2025-08-26 19:51:57,097 - Main - INFO - 开始第 1/40 轮模拟
2025-08-26 19:52:00,244 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信念到 round_1_before_beliefs.json
2025-08-26 19:52:00,575 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户记忆到 round_1_before_memories.json
2025-08-26 19:52:00,883 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信息到 round_1_before_users.json，共 100 个用户
2025-08-26 19:52:06,250 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有帖子、评论和回复到 round_1_before_posts.json
2025-08-26 19:52:06,261 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有评论和回复到 round_1_before_comments.json，共 21 条
2025-08-26 19:52:06,262 - ExperimentLogger - INFO - 开始记录轮次 1 before 阶段的实验指标...
2025-08-26 19:53:24,636 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的SIR指标到 round_1_before_sir_metrics.json
2025-08-26 19:53:24,636 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-26 19:53:24,637 - ExperimentLogger - INFO -   - 总体易感者: 94 (0.940) - 从未发布谣言内容
2025-08-26 19:53:24,637 - ExperimentLogger - INFO -   - 总体感染者: 5 (0.050) - 发布过谣言但未发布非谣言
2025-08-26 19:53:24,637 - ExperimentLogger - INFO -   - 总体康复者: 1 (0.010) - 曾经是感染者且后来发布了非谣言
2025-08-26 19:53:24,637 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=0, 易感者=0(0.000), 感染者=0(0.000), 康复者=0(0.000)
2025-08-26 19:53:24,637 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=100, 易感者=94(0.940), 感染者=5(0.050), 康复者=1(0.010)
2025-08-26 19:53:25,219 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的极化指数到 round_1_before_polarization_metrics.json
2025-08-26 19:53:25,219 - ExperimentLogger - INFO -   - 总体OEI指数: 0.000
2025-08-26 19:53:25,219 - ExperimentLogger - INFO -   - 极端观点用户: 0 (0.000)
2025-08-26 19:53:25,219 - ExperimentLogger - INFO -   - 温和观点用户: 0 (0.000)
2025-08-26 19:53:25,220 - ExperimentLogger - INFO -   - 无明确观点用户: 100 (1.000)
2025-08-26 19:53:25,220 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 0.000 (总数: 0, 极端: 0, 温和: 0, 无观点: 0)
2025-08-26 19:53:25,220 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.000 (总数: 100, 极端: 0, 温和: 0, 无观点: 100)
2025-08-26 19:53:26,073 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的智能体演化数据到 round_1_before_agent_evolution.json
2025-08-26 19:53:26,073 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-26 19:53:26,090 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的网络传播分析到 round_1_before_network_propagation.json
2025-08-26 19:53:26,090 - ExperimentLogger - INFO -   - 活跃用户数: 9
2025-08-26 19:53:26,090 - ExperimentLogger - INFO -   - 内容创作者数: 9
2025-08-26 19:53:26,090 - ExperimentLogger - INFO -   - 评论者数: 0
2025-08-26 19:53:26,090 - ExperimentLogger - INFO -   - 总帖子数: 10
2025-08-26 19:53:26,091 - ExperimentLogger - INFO -   - 总评论数: 21
2025-08-26 19:53:26,091 - ExperimentLogger - INFO - 轮次 1 before 阶段的实验指标记录完成
2025-08-26 19:53:26,091 - Simulator - INFO - 设置轮数信息: 当前轮次 1/40
2025-08-26 19:53:26,091 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-26 19:53:26,091 - Simulator - INFO - Simulation step 1/5
2025-08-26 19:53:26,373 - Simulator - WARNING - 用户 user_40 没有信念，使用默认信念ID
2025-08-26 19:53:28,725 - Simulator - INFO - Added CREATE_POST event for user user_40 to queue. Queue size: 1
2025-08-26 19:53:28,725 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_40
2025-08-26 19:53:28,726 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_40' -----
2025-08-26 19:53:37,805 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_40_1c004690).
2025-08-26 19:53:37,805 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 19:53:42,450 - src.modules.content_module - INFO - 帖子 'post_04dba374' 由用户 'user_40' 创建成功。
2025-08-26 19:53:42,451 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:53:42,454 - NotificationModule - INFO - Would send notification to user 'user_26': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,462 - Simulator - INFO - Added READ_POST event for user user_26 to queue. Queue size: 1
2025-08-26 19:53:42,462 - Simulator - INFO - Generated READ_POST event for user user_26 based on notification.
2025-08-26 19:53:42,462 - NotificationModule - INFO - Would send notification to user 'user_25': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,470 - Simulator - INFO - Added READ_POST event for user user_25 to queue. Queue size: 2
2025-08-26 19:53:42,470 - Simulator - INFO - Generated READ_POST event for user user_25 based on notification.
2025-08-26 19:53:42,471 - NotificationModule - INFO - Would send notification to user 'user_94': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,479 - Simulator - INFO - Added READ_POST event for user user_94 to queue. Queue size: 3
2025-08-26 19:53:42,479 - Simulator - INFO - Generated READ_POST event for user user_94 based on notification.
2025-08-26 19:53:42,479 - NotificationModule - INFO - Would send notification to user 'user_62': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,487 - Simulator - INFO - Added READ_POST event for user user_62 to queue. Queue size: 4
2025-08-26 19:53:42,487 - Simulator - INFO - Generated READ_POST event for user user_62 based on notification.
2025-08-26 19:53:42,487 - NotificationModule - INFO - Would send notification to user 'user_89': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,495 - Simulator - INFO - Added READ_POST event for user user_89 to queue. Queue size: 5
2025-08-26 19:53:42,495 - Simulator - INFO - Generated READ_POST event for user user_89 based on notification.
2025-08-26 19:53:42,496 - NotificationModule - INFO - Would send notification to user 'user_37': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,504 - Simulator - INFO - Added READ_POST event for user user_37 to queue. Queue size: 6
2025-08-26 19:53:42,504 - Simulator - INFO - Generated READ_POST event for user user_37 based on notification.
2025-08-26 19:53:42,504 - NotificationModule - INFO - Would send notification to user 'user_39': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,512 - Simulator - INFO - Added READ_POST event for user user_39 to queue. Queue size: 7
2025-08-26 19:53:42,512 - Simulator - INFO - Generated READ_POST event for user user_39 based on notification.
2025-08-26 19:53:42,512 - NotificationModule - INFO - Would send notification to user 'user_35': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,520 - Simulator - INFO - Added READ_POST event for user user_35 to queue. Queue size: 8
2025-08-26 19:53:42,520 - Simulator - INFO - Generated READ_POST event for user user_35 based on notification.
2025-08-26 19:53:42,520 - NotificationModule - INFO - Would send notification to user 'user_47': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,528 - Simulator - INFO - Added READ_POST event for user user_47 to queue. Queue size: 9
2025-08-26 19:53:42,529 - Simulator - INFO - Generated READ_POST event for user user_47 based on notification.
2025-08-26 19:53:42,529 - NotificationModule - INFO - Would send notification to user 'user_77': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,537 - Simulator - INFO - Added READ_POST event for user user_77 to queue. Queue size: 10
2025-08-26 19:53:42,537 - Simulator - INFO - Generated READ_POST event for user user_77 based on notification.
2025-08-26 19:53:42,537 - NotificationModule - INFO - Would send notification to user 'user_19': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,545 - Simulator - INFO - Added READ_POST event for user user_19 to queue. Queue size: 11
2025-08-26 19:53:42,545 - Simulator - INFO - Generated READ_POST event for user user_19 based on notification.
2025-08-26 19:53:42,545 - NotificationModule - INFO - Would send notification to user 'user_2': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,553 - Simulator - INFO - Added READ_POST event for user user_2 to queue. Queue size: 12
2025-08-26 19:53:42,553 - Simulator - INFO - Generated READ_POST event for user user_2 based on notification.
2025-08-26 19:53:42,553 - NotificationModule - INFO - Would send notification to user 'user_30': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,561 - Simulator - INFO - Added READ_POST event for user user_30 to queue. Queue size: 13
2025-08-26 19:53:42,562 - Simulator - INFO - Generated READ_POST event for user user_30 based on notification.
2025-08-26 19:53:42,562 - NotificationModule - INFO - Would send notification to user 'user_72': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,570 - Simulator - INFO - Added READ_POST event for user user_72 to queue. Queue size: 14
2025-08-26 19:53:42,570 - Simulator - INFO - Generated READ_POST event for user user_72 based on notification.
2025-08-26 19:53:42,570 - NotificationModule - INFO - Would send notification to user 'user_70': User 'user_40' you follow has published a new post 'post_04dba374'.
MemoryModule initialized.

【模拟步骤】选择用户: user_40 执行操作 (步骤 1/5, 轮次 1/40)
Warning: Belief 'default_belief_id' not found.
【用户行为】用户 user_40 选择了 CREATE_POST 操作，内容："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: CREATE_POST (用户 user_40), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_40)，发布内容："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【引擎】开始处理用户 user_40 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_40'...
MemoryModule: Successfully created memory 'mem_user_40_1c004690'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_40_1c004690)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_40 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_26
【通知事件】生成通知事件: 用户 user_26 接收 READ_POST 通知
【通知事件】为用户 user_26 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_26), 队列大小: 1
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_25
【通知事件】生成通知事件: 用户 user_25 接收 READ_POST 通知
【通知事件】为用户 user_25 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_25), 队列大小: 2
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_94
【通知事件】生成通知事件: 用户 user_94 接收 READ_POST 通知
【通知事件】为用户 user_94 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_94), 队列大小: 3
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_62
【通知事件】生成通知事件: 用户 user_62 接收 READ_POST 通知
【通知事件】为用户 user_62 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_62), 队列大小: 4
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_89
【通知事件】生成通知事件: 用户 user_89 接收 READ_POST 通知
【通知事件】为用户 user_89 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_89), 队列大小: 5
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_37
【通知事件】生成通知事件: 用户 user_37 接收 READ_POST 通知
【通知事件】为用户 user_37 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_37), 队列大小: 6
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_39
【通知事件】生成通知事件: 用户 user_39 接收 READ_POST 通知
【通知事件】为用户 user_39 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_39), 队列大小: 7
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_35
【通知事件】生成通知事件: 用户 user_35 接收 READ_POST 通知
【通知事件】为用户 user_35 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_35), 队列大小: 8
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_47
【通知事件】生成通知事件: 用户 user_47 接收 READ_POST 通知
【通知事件】为用户 user_47 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_47), 队列大小: 9
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_77
【通知事件】生成通知事件: 用户 user_77 接收 READ_POST 通知
【通知事件】为用户 user_77 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_77), 队列大小: 10
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_19
【通知事件】生成通知事件: 用户 user_19 接收 READ_POST 通知
【通知事件】为用户 user_19 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_19), 队列大小: 11
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_2
【通知事件】生成通知事件: 用户 user_2 接收 READ_POST 通知
【通知事件】为用户 user_2 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_2), 队列大小: 12
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_30
【通知事件】生成通知事件: 用户 user_30 接收 READ_POST 通知
【通知事件】为用户 user_30 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_30), 队列大小: 13
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_72
【通知事件】生成通知事件: 用户 user_72 接收 READ_POST 通知
【通知事件】为用户 user_72 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_72), 队列大小: 14
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_70
【通知事件】生成通知事件: 用户 user_70 接收 READ_POST 通知
2025-08-26 19:53:42,578 - Simulator - INFO - Added READ_POST event for user user_70 to queue. Queue size: 15
2025-08-26 19:53:42,578 - Simulator - INFO - Generated READ_POST event for user user_70 based on notification.
2025-08-26 19:53:42,578 - NotificationModule - INFO - Would send notification to user 'user_95': User 'user_40' you follow has published a new post 'post_04dba374'.
2025-08-26 19:53:42,586 - Simulator - INFO - Added READ_POST event for user user_95 to queue. Queue size: 16
2025-08-26 19:53:42,586 - Simulator - INFO - Generated READ_POST event for user user_95 based on notification.
2025-08-26 19:53:42,586 - Engine - INFO - Sent new post notification to 16 followers.
2025-08-26 19:53:42,586 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_40'...
2025-08-26 19:53:42,587 - src.modules.belief_module - INFO - 开始处理用户 'user_40' 的认知流程...
2025-08-26 19:53:42,602 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:53:42,602 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:53:42,603 - src.modules.belief_module - INFO - 用户 'user_40' 的短期记忆中未找到显著议题簇
2025-08-26 19:53:42,603 - Engine - INFO - Cognitive processing for user 'user_40' finished.
2025-08-26 19:53:42,603 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_40' processed successfully. -----
2025-08-26 19:53:42,604 - Simulator - INFO - Processing event from queue: READ_POST for user user_26
2025-08-26 19:53:42,604 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_26' -----
2025-08-26 19:53:49,384 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_26_fa0053b2).
2025-08-26 19:53:49,384 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:53:52,189 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:53:52,189 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:53:52,189 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:53:52,189 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_26'...
2025-08-26 19:53:52,189 - src.modules.belief_module - INFO - 开始处理用户 'user_26' 的认知流程...
2025-08-26 19:53:52,204 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:53:52,205 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:53:52,205 - src.modules.belief_module - INFO - 用户 'user_26' 的短期记忆中未找到显著议题簇
2025-08-26 19:53:52,205 - Engine - INFO - Cognitive processing for user 'user_26' finished.
2025-08-26 19:53:52,205 - Engine - INFO - ----- Action 'READ_POST' for user 'user_26' processed successfully. -----
2025-08-26 19:53:52,206 - Simulator - INFO - Processing event from queue: READ_POST for user user_25
2025-08-26 19:53:52,206 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_25' -----
2025-08-26 19:53:59,019 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_25_e8d3c519).
2025-08-26 19:53:59,020 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:01,883 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:01,883 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:01,883 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:01,883 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_25'...
2025-08-26 19:54:01,883 - src.modules.belief_module - INFO - 开始处理用户 'user_25' 的认知流程...
2025-08-26 19:54:01,898 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:01,899 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:01,899 - src.modules.belief_module - INFO - 用户 'user_25' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:01,899 - Engine - INFO - Cognitive processing for user 'user_25' finished.
2025-08-26 19:54:01,899 - Engine - INFO - ----- Action 'READ_POST' for user 'user_25' processed successfully. -----
2025-08-26 19:54:01,900 - Simulator - INFO - Processing event from queue: READ_POST for user user_94
2025-08-26 19:54:01,900 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_94' -----
2025-08-26 19:54:08,943 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_94_1da735f4).
2025-08-26 19:54:08,943 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:11,809 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:11,809 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:11,810 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:11,810 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_94'...
2025-08-26 19:54:11,810 - src.modules.belief_module - INFO - 开始处理用户 'user_94' 的认知流程...
2025-08-26 19:54:11,825 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:11,825 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:11,825 - src.modules.belief_module - INFO - 用户 'user_94' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:11,826 - Engine - INFO - Cognitive processing for user 'user_94' finished.
2025-08-26 19:54:11,826 - Engine - INFO - ----- Action 'READ_POST' for user 'user_94' processed successfully. -----
2025-08-26 19:54:11,826 - Simulator - INFO - Processing event from queue: READ_POST for user user_62
2025-08-26 19:54:11,826 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_62' -----
2025-08-26 19:54:18,590 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_62_ac3b1ac9).
2025-08-26 19:54:18,590 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:21,435 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:21,436 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:21,436 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:21,436 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_62'...
2025-08-26 19:54:21,436 - src.modules.belief_module - INFO - 开始处理用户 'user_62' 的认知流程...
2025-08-26 19:54:21,451 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:21,451 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:21,452 - src.modules.belief_module - INFO - 用户 'user_62' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:21,452 - Engine - INFO - Cognitive processing for user 'user_62' finished.
2025-08-26 19:54:21,452 - Engine - INFO - ----- Action 'READ_POST' for user 'user_62' processed successfully. -----
2025-08-26 19:54:21,452 - Simulator - INFO - Processing event from queue: READ_POST for user user_89
2025-08-26 19:54:21,453 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_89' -----
2025-08-26 19:54:28,433 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_89_2c73523b).
2025-08-26 19:54:28,433 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:31,238 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:31,238 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:31,238 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:31,238 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_89'...
2025-08-26 19:54:31,238 - src.modules.belief_module - INFO - 开始处理用户 'user_89' 的认知流程...
2025-08-26 19:54:31,253 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:31,254 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:31,254 - src.modules.belief_module - INFO - 用户 'user_89' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:31,254 - Engine - INFO - Cognitive processing for user 'user_89' finished.
2025-08-26 19:54:31,254 - Engine - INFO - ----- Action 'READ_POST' for user 'user_89' processed successfully. -----
2025-08-26 19:54:31,255 - Simulator - INFO - Processing event from queue: READ_POST for user user_37
【通知事件】为用户 user_70 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_70), 队列大小: 15
【通知模块】用户 user_40 发布了新帖子，发送通知给关注者 user_95
【通知事件】生成通知事件: 用户 user_95 接收 READ_POST 通知
【通知事件】为用户 user_95 生成浏览事件，浏览帖子 post_04dba374："最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，婚姻是两个人的事，但继承这事，多少还是得考虑现实..."
【事件队列】添加事件: READ_POST (用户 user_95), 队列大小: 16
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_40 的认知处理...

【认知处理】开始处理用户 user_40 的认知流程
【阶段1+2】获取用户 user_40 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_40'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_40 的认知处理完成
【引擎】用户 user_40 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_40)
【处理事件】从队列中处理事件: READ_POST (用户 user_26)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
【引擎】开始处理用户 user_26 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_26'...
MemoryModule: Successfully created memory 'mem_user_26_fa0053b2'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_26_fa0053b2)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_26 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_26 的认知处理...

【认知处理】开始处理用户 user_26 的认知流程
【阶段1+2】获取用户 user_26 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_26'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_26 的认知处理完成
【引擎】用户 user_26 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_26)
【处理事件】从队列中处理事件: READ_POST (用户 user_25)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
【引擎】开始处理用户 user_25 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_25'...
MemoryModule: Successfully created memory 'mem_user_25_e8d3c519'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_25_e8d3c519)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_25 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_25 的认知处理...

【认知处理】开始处理用户 user_25 的认知流程
【阶段1+2】获取用户 user_25 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_25'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_25 的认知处理完成
【引擎】用户 user_25 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_25)
【处理事件】从队列中处理事件: READ_POST (用户 user_94)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
【引擎】开始处理用户 user_94 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_94'...
MemoryModule: Successfully created memory 'mem_user_94_1da735f4'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_94_1da735f4)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_94 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_94 的认知处理...

【认知处理】开始处理用户 user_94 的认知流程
【阶段1+2】获取用户 user_94 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_94'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_94 的认知处理完成
【引擎】用户 user_94 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_94)
【处理事件】从队列中处理事件: READ_POST (用户 user_62)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
【引擎】开始处理用户 user_62 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_62'...
MemoryModule: Successfully created memory 'mem_user_62_ac3b1ac9'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_62_ac3b1ac9)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_62 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_62 的认知处理...

【认知处理】开始处理用户 user_62 的认知流程
【阶段1+2】获取用户 user_62 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_62'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_62 的认知处理完成
【引擎】用户 user_62 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_62)
【处理事件】从队列中处理事件: READ_POST (用户 user_89)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
【引擎】开始处理用户 user_89 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_89'...
MemoryModule: Successfully created memory 'mem_user_89_2c73523b'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_89_2c73523b)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_89 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_89 的认知处理...

【认知处理】开始处理用户 user_89 的认知流程
【阶段1+2】获取用户 user_89 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_89'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_89 的认知处理完成
【引擎】用户 user_89 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_89)
【处理事件】从队列中处理事件: READ_POST (用户 user_37)，浏览帖子 post_04dba374："您关注的用户 user_40 发布了新帖子:

最近和朋友聊起婚姻和继承，有点感慨。其实我一直觉得，..."
2025-08-26 19:54:31,255 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_37' -----
2025-08-26 19:54:37,735 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_37_3b0567b3).
2025-08-26 19:54:37,735 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:40,689 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:40,690 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:40,690 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:40,690 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_37'...
2025-08-26 19:54:40,690 - src.modules.belief_module - INFO - 开始处理用户 'user_37' 的认知流程...
2025-08-26 19:54:40,705 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:40,705 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:40,706 - src.modules.belief_module - INFO - 用户 'user_37' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:40,706 - Engine - INFO - Cognitive processing for user 'user_37' finished.
2025-08-26 19:54:40,706 - Engine - INFO - ----- Action 'READ_POST' for user 'user_37' processed successfully. -----
2025-08-26 19:54:40,706 - Simulator - INFO - Processing event from queue: READ_POST for user user_39
2025-08-26 19:54:40,707 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_39' -----
2025-08-26 19:54:47,543 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_39_12c91820).
2025-08-26 19:54:47,543 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 19:54:50,713 - src.modules.content_module - INFO - Incremented view count for post 'post_04dba374'.
2025-08-26 19:54:50,713 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 19:54:50,713 - Engine - INFO - No notification needed for this action type.
2025-08-26 19:54:50,713 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_39'...
2025-08-26 19:54:50,713 - src.modules.belief_module - INFO - 开始处理用户 'user_39' 的认知流程...
2025-08-26 19:54:50,728 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 19:54:50,728 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 19:54:50,729 - src.modules.belief_module - INFO - 用户 'user_39' 的短期记忆中未找到显著议题簇
2025-08-26 19:54:50,729 - Engine - INFO - Cognitive processing for user 'user_39' finished.
2025-08-26 19:54:50,729 - Engine - INFO - ----- Action 'READ_POST' for user 'user_39' processed successfully. -----
2025-08-26 19:54:50,729 - Simulator - INFO - Processing event from queue: READ_POST for user user_35
2025-08-26 19:54:50,730 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_35' -----
