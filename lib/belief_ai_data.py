#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信念AI数据加载器
从JSON文件加载预处理的AI数据
"""

import json
import os

# 全局变量存储加载的数据
_BELIEF_AI_DATA = None

def _load_belief_ai_data():
    """加载信念AI数据"""
    global _BELIEF_AI_DATA
    if _BELIEF_AI_DATA is not None:
        return _BELIEF_AI_DATA
    
    # 构建JSON文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, "科技", "belief_ai_data.json")
    
    try:
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                _BELIEF_AI_DATA = json.load(f)
        else:
            _BELIEF_AI_DATA = {}
    except Exception as e:
        print(f"加载信念AI数据失败: {e}")
        _BELIEF_AI_DATA = {}
    
    return _BELIEF_AI_DATA

def get_belief_ai_data(belief_id: int, source: str):
    """获取指定信念和来源的AI数据"""
    belief_data = _load_belief_ai_data()
    belief_id_str = str(belief_id)
    
    if belief_id_str in belief_data:
        belief_info = belief_data[belief_id_str]
        if "sources" in belief_info and source in belief_info["sources"]:
            return belief_info["sources"][source]
    return None

def get_emotional_disposition(belief_id: int, source: str):
    """获取指定信念和来源的情感倾向"""
    ai_data = get_belief_ai_data(belief_id, source)
    if ai_data and "emotional_disposition" in ai_data:
        return ai_data["emotional_disposition"]
    
    return {
        "joy": 0.5, "sadness": 0.5, "anger": 0.5, "fear": 0.5,
        "surprise": 0.5, "trust": 0.5, "disgust": 0.5, "anticipation": 0.5
    }

def get_embedding(belief_id: int, source: str):
    """获取指定信念和来源的嵌入向量"""
    ai_data = get_belief_ai_data(belief_id, source)
    if ai_data and "embedding" in ai_data:
        return ai_data["embedding"]
    return None

def is_data_available():
    """检查AI数据是否可用"""
    belief_data = _load_belief_ai_data()
    return len(belief_data) > 0 and any(
        "sources" in belief_info for belief_info in belief_data.values()
    )
