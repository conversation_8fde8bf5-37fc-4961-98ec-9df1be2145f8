#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
预处理信念数据脚本
为lib/beliefs.py中的每个信念调用AI服务获取情感向量和嵌入向量，
并将结果保存到lib/belief_ai_data.py中供后续使用
"""

import sys
import os
import json
import logging
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.ai_service import AIService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BeliefPreprocessor")

class BeliefPreprocessor:
    def __init__(self):
        """初始化信念预处理器"""
        self.ai_service = AIService(
            api_key="sk-mT8uDsuxCwxMPUlwdodDOcdjzJjOYFBQQWw17LLLDPXyqVH0",
            base_url="http://47.102.193.166:8060",
            embedding_url="http://10.201.64.106:30000/v1",
            reranker_url="http://10.201.64.106:30001/v1"
        )
        self.processed_beliefs = {}
        self.beliefs_data = self._load_beliefs_data()

    def _load_beliefs_data(self):
        """加载信念数据"""
        beliefs_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "lib", "科技", "belief.json"
        )

        try:
            with open(beliefs_file, 'r', encoding='utf-8') as f:
                beliefs_data = json.load(f)
            logger.info(f"成功加载 {len(beliefs_data)} 个信念数据")
            return beliefs_data
        except Exception as e:
            logger.error(f"加载信念数据失败: {e}")
            return []
        
    def process_all_beliefs(self):
        """处理所有信念，获取AI数据"""
        logger.info(f"开始预处理 {len(self.beliefs_data)} 个信念...")

        total_beliefs = len(self.beliefs_data)
        processed_count = 0
        failed_count = 0

        for belief in self.beliefs_data:
            belief_id = belief["id"]
            logger.info(f"处理信念 {belief_id}/{total_beliefs}: {belief['proposition'][:50]}...")
            
            try:
                # 处理每个信念的三个来源
                belief_ai_data = {
                    "id": belief_id,
                    "sources": {}
                }
                
                sources = ["proposition", "oppositional_view", "vernacular_saying"]
                
                for source in sources:
                    if source in belief:
                        text = belief[source]
                        logger.info(f"  处理来源 '{source}': {text[:30]}...")
                        
                        # 获取情感向量
                        try:
                            emotional_disposition = self.ai_service.analyze_emotional_impact(text)
                            logger.debug(f"    成功获取情感向量")
                            
                            # 确保所有必需的情感维度都存在
                            required_emotions = ["joy", "sadness", "anger", "fear", "surprise", "trust", "disgust", "anticipation"]
                            for emotion in required_emotions:
                                if emotion not in emotional_disposition:
                                    emotional_disposition[emotion] = 0.5
                                # 确保值在0-1范围内
                                emotional_disposition[emotion] = max(0.0, min(1.0, float(emotional_disposition[emotion])))
                                emotional_disposition[emotion] = round(emotional_disposition[emotion], 2)
                                
                        except Exception as e:
                            logger.warning(f"    获取情感向量失败: {e}")
                            emotional_disposition = {
                                "joy": 0.5, "sadness": 0.5, "anger": 0.5, "fear": 0.5,
                                "surprise": 0.5, "trust": 0.5, "disgust": 0.5, "anticipation": 0.5
                            }
                        
                        # 获取嵌入向量
                        try:
                            embedding = self.ai_service.get_embedding(text)
                            logger.debug(f"    成功获取嵌入向量")
                        except Exception as e:
                            logger.warning(f"    获取嵌入向量失败: {e}")
                            # 使用随机向量作为备选
                            import random
                            embedding = [random.uniform(-1, 1) for _ in range(4096)]
                        
                        belief_ai_data["sources"][source] = {
                            "text": text,
                            "emotional_disposition": emotional_disposition,
                            "embedding": embedding
                        }
                        
                        logger.debug(f"    来源 '{source}' 处理完成")
                
                self.processed_beliefs[str(belief_id)] = belief_ai_data
                processed_count += 1
                
                logger.info(f"信念 {belief_id} 处理完成 ({processed_count}/{total_beliefs})")
                
            except Exception as e:
                logger.error(f"处理信念 {belief_id} 失败: {e}")
                failed_count += 1
                continue
        
        logger.info(f"信念预处理完成: 成功 {processed_count} 个，失败 {failed_count} 个")
        return self.processed_beliefs
    
    def save_to_file(self, output_path: str):
        """将处理结果保存到JSON文件"""
        logger.info(f"保存预处理结果到 {output_path}...")

        # 直接保存为JSON格式
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.processed_beliefs, f, ensure_ascii=False, indent=2)
            logger.info(f"预处理结果已保存到 {output_path}")
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise

def main():
    """主函数"""
    logger.info("开始信念预处理...")
    
    # 创建预处理器
    preprocessor = BeliefPreprocessor()
    
    # 处理所有信念
    processed_data = preprocessor.process_all_beliefs()
    
    if not processed_data:
        logger.error("没有成功处理任何信念数据")
        return
    
    # 保存到与原始信念数据相同的目录
    output_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "lib", "科技",
        "belief_ai_data.json"
    )
    
    preprocessor.save_to_file(output_path)
    
    logger.info("信念预处理完成!")
    logger.info(f"处理了 {len(processed_data)} 个信念")
    logger.info(f"结果保存在: {output_path}")

if __name__ == "__main__":
    main()
